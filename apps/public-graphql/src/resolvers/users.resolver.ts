import { Context, Parent, Query, ResolveField, Resolver, Mutation, Field, ObjectType } from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';
import { User } from 'libs/internal/users/entities/user.entity';
import { UsersService } from 'libs/internal/users/users.service';
import { GqlAuthGuard } from 'libs/internal/auth/auth.guard';
import { UserManagedWalletDTO } from 'libs/internal/users/dto/managed-wallet.dto';
import { UserEmbeddedWalletDTO } from 'libs/internal/users/dto/embedded-wallet.dto';
import { TurnkeyService } from '@lib/internal/turnkey';

@ObjectType()
export class MarkAccountDeleteResponse {
    @Field(() => Boolean)
    success: boolean;

    @Field({ nullable: true })
    deletedAt?: Date;
}

@ObjectType()
export class ReactivateAccountResponse {
    @Field(() => Boolean)
    success: boolean;

    @Field()
    reactivatedAt: Date;
}

@Resolver(() => User)
export class UsersResolver {
    constructor(
        private readonly usersService: UsersService,
        private readonly turnkeyService: TurnkeyService,
    ) {}

    @UseGuards(GqlAuthGuard)
    @Query(() => User)
    async account(@Context() context: any): Promise<User> {
        return this.usersService.getUser(context.req.user.sub);
    }

    @ResolveField(() => [UserManagedWalletDTO])
    async userManagedWallets(@Parent() user: User) {
        return this.usersService.getUserManagedWallets(user);
    }

    @ResolveField(() => [UserEmbeddedWalletDTO])
    async userEmbeddedWallets(@Parent() user: User) {
        const wallets = await this.turnkeyService.getTurnkeyWallets(user);

        const accounts = await this.usersService.syncUserEmbeddedWallets(user, wallets);

        return accounts.map((account) => {
            return {
                ...account,
                balance: 0,
            } as UserEmbeddedWalletDTO;
        });
    }

    @UseGuards(GqlAuthGuard)
    @Mutation(() => MarkAccountDeleteResponse)
    async markAccountDelete(@Context() context: any): Promise<MarkAccountDeleteResponse> {
        return this.usersService.markAccountDelete(context.req.user.sub);
    }

    @UseGuards(GqlAuthGuard)
    @Mutation(() => ReactivateAccountResponse)
    async reactivateAccount(@Context() context: any): Promise<ReactivateAccountResponse> {
        return this.usersService.reactivateAccount(context.req.user.sub);
    }
}
